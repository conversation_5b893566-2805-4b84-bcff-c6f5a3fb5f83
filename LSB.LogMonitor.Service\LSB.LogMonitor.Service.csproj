﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>dotnet-LSB.LogMonitor.Service-488b1010-379d-47d4-ab1d-1010ef25f1a1</UserSecretsId>
    <AssemblyTitle>LSB Telemetry Service</AssemblyTitle>
    <Product>LSB Telemetry Service</Product>
    <Company>LSB</Company>
    <Description>LSB Telemetry Service - System monitoring and error notifications</Description>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="8.0.1" />
    <PackageReference Include="System.Management" Version="8.0.0" />
    <PackageReference Include="System.Diagnostics.PerformanceCounter" Version="8.0.0" />
    <PackageReference Include="System.Text.Json" Version="9.0.5" />
    <PackageReference Include="YamlDotNet" Version="16.3.0" />
  </ItemGroup>
</Project>
