@echo off
echo ================================================
echo Creating LSB Log Monitor Service Package
echo ================================================

REM Set package name with timestamp
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
set "timestamp=%YYYY%-%MM%-%DD%_%HH%-%Min%-%Sec%"

set PACKAGE_NAME=LSB.LogMonitor.Service.Console_%timestamp%
set PACKAGE_DIR=%PACKAGE_NAME%
set SOURCE_DIR=LSB.LogMonitor.Service\bin\Release\net8.0

echo Package Name: %PACKAGE_NAME%
echo Source Directory: %SOURCE_DIR%
echo.

REM Check if source directory exists
if not exist "%SOURCE_DIR%" (
    echo ERROR: Source directory not found: %SOURCE_DIR%
    echo Please build the project first:
    echo   dotnet build -c Release
    echo Or use Visual Studio to build in Release mode.
    pause
    exit /b 1
)

echo Source directory found - OK
echo.

REM Clean up any existing package directory
if exist "%PACKAGE_DIR%" (
    echo Cleaning up existing package directory...
    rmdir /s /q "%PACKAGE_DIR%"
)

REM Create package directory
echo Creating package directory: %PACKAGE_DIR%
mkdir "%PACKAGE_DIR%"

REM Copy all files from Release build
echo Copying application files...
xcopy "%SOURCE_DIR%\*" "%PACKAGE_DIR%\" /E /I /H /Y

if %errorLevel% neq 0 (
    echo ERROR: Failed to copy application files
    pause
    exit /b 1
)

echo ✓ Application files copied successfully
echo.

REM Copy Scripts directory
echo Copying Scripts directory...
xcopy "LSB.LogMonitor.Service\Scripts\*" "%PACKAGE_DIR%\Scripts\" /E /I /H /Y

if %errorLevel% neq 0 (
    echo ERROR: Failed to copy Scripts directory
    pause
    exit /b 1
)

echo ✓ Scripts copied successfully
echo.

REM Copy README
echo Copying README...
copy "LSB.LogMonitor.Service\README.md" "%PACKAGE_DIR%\README.md"

if %errorLevel% neq 0 (
    echo WARNING: Failed to copy README.md
) else (
    echo ✓ README copied successfully
)
echo.

REM Create installation guide
echo Creating installation guide...
(
echo # LSB Log Monitor Service - Quick Start Guide
echo.
echo ## 🚀 Quick Installation
echo.
echo ### Step 1: Extract files
echo Extract all files to a folder, e.g., C:\LSB\LogMonitor\
echo.
echo ### Step 2: Configure settings
echo Edit appsettings.json:
echo - Set your Telegram Bot Token
echo - Set your Chat IDs
echo - Configure log paths
echo.
echo ### Step 3: Install and start service
echo Right-click on Scripts\StartService.bat and select "Run as administrator"
echo.
echo ## 📋 Available Scripts
echo.
echo - **StartService.bat** - Install and start the service
echo - **StopService.bat** - Stop the service
echo - **UninstallService.bat** - Remove the service
echo.
echo ## 🔧 Test Mode
echo To test configuration before installing:
echo ```
echo LSB.LogMonitor.Service.exe --test
echo ```
echo.
echo ## 📖 Full Documentation
echo See README.md for complete documentation.
echo.
echo ## ⚠️ Important Notes
echo.
echo 1. **Always run scripts as Administrator**
echo 2. **Configure appsettings.json before installing**
echo 3. **Ensure .NET 8.0 Runtime is installed**
echo 4. **Check Windows Event Viewer for service logs**
echo.
echo ## 🆘 Support
echo.
echo If you encounter issues:
echo 1. Check Windows Event Viewer
echo 2. Run test mode to verify configuration
echo 3. Ensure all paths in appsettings.json are correct
echo 4. Verify Telegram bot token and chat IDs
) > "%PACKAGE_DIR%\QUICK_START.md"

echo ✓ Quick start guide created
echo.

REM Create version info
echo Creating version info...
(
echo LSB Log Monitor Service - Console Version
echo ========================================
echo.
echo Build Date: %timestamp%
echo Version: Console Application
echo.
echo Package Contents:
echo - LSB.LogMonitor.Service.exe ^(Main application^)
echo - appsettings.json ^(Configuration^)
echo - Scripts\ ^(Service management scripts^)
echo - README.md ^(Full documentation^)
echo - QUICK_START.md ^(Quick installation guide^)
echo - All required DLL dependencies
echo.
echo Installation:
echo 1. Extract to desired location
echo 2. Configure appsettings.json
echo 3. Run Scripts\StartService.bat as Administrator
echo.
echo For support and documentation, see README.md
) > "%PACKAGE_DIR%\VERSION_INFO.txt"

echo ✓ Version info created
echo.

REM Show package contents
echo Package contents:
dir "%PACKAGE_DIR%" /B
echo.
echo Scripts directory:
dir "%PACKAGE_DIR%\Scripts" /B
echo.

REM Create ZIP file using PowerShell
echo Creating ZIP file...
powershell -command "Compress-Archive -Path '%PACKAGE_DIR%\*' -DestinationPath '%PACKAGE_NAME%.zip' -Force"

if %errorLevel% equ 0 (
    echo ✓ ZIP file created successfully: %PACKAGE_NAME%.zip
    
    REM Get file size
    for %%A in ("%PACKAGE_NAME%.zip") do (
        set size=%%~zA
        set /a sizeMB=!size!/1024/1024
    )
    
    echo File size: %sizeMB% MB
    echo.
    
    REM Clean up temporary directory
    echo Cleaning up temporary files...
    rmdir /s /q "%PACKAGE_DIR%"
    echo ✓ Cleanup completed
    echo.
    
    echo ================================================
    echo PACKAGE CREATED SUCCESSFULLY!
    echo ================================================
    echo.
    echo Package file: %PACKAGE_NAME%.zip
    echo.
    echo This package contains:
    echo ✓ Console application executable
    echo ✓ All required dependencies
    echo ✓ Service management scripts
    echo ✓ Configuration files
    echo ✓ Documentation
    echo.
    echo To deploy:
    echo 1. Extract ZIP file to target location
    echo 2. Configure appsettings.json
    echo 3. Run Scripts\StartService.bat as Administrator
    echo.
    
) else (
    echo ✗ Failed to create ZIP file
    echo You can manually zip the contents of: %PACKAGE_DIR%
)

echo.
pause
