# LSB Log Monitor Service - Hướng dẫn cài đặt

## 🚀 Cài đặt nhanh (5 phút)

### Bước 1: Gi<PERSON>i nén file
```
1. <PERSON><PERSON><PERSON><PERSON> nén file ZIP đến thư mục mong muốn
   Ví dụ: C:\LSB\LogMonitor\
2. Ki<PERSON>m tra có đầy đủ files:
   ✅ LSB.LogMonitor.Service.exe
   ✅ appsettings.json  
   ✅ Scripts\ (thư mục)
```

### Bước 2: C<PERSON>u hình
```
1. Mở file appsettings.json bằng Notepad
2. Thay đổi các thông tin sau:
   - "BotToken": "TOKEN_TELEGRAM_BOT_CỦA_BẠN"
   - "MainChatId": "CHAT_ID_KÊNH_CHÍNH"
   - "LogRootPath": "ĐƯỜNG_DẪN_THƯ_MỤC_LOGS"
3. Lưu file (Ctrl+S)
```

### Bước 3: Cài đặt service
```
1. <PERSON><PERSON> chuột phải vào <PERSON>ripts\StartService.bat
2. <PERSON>ọn "Run as administrator"
3. <PERSON><PERSON> thông báo "SERVICE INSTALLATION COMPLETED!"
```

## ✅ Hoàn thành!
Service đã được cài đặt và sẽ tự động:
- Chạy ngay lập tức
- Khởi động cùng Windows
- Gửi thông báo lỗi qua Telegram

---

## 🔧 Quản lý service

### Dừng service
```
Scripts\StopService.bat (Run as administrator)
```

### Gỡ cài đặt service
```
Scripts\UninstallService.bat (Run as administrator)
```

### Kiểm tra trạng thái
```
1. Mở Services.msc
2. Tìm "LSB Log Monitor Service"
3. Xem Status: Running/Stopped
```

---

## 🧪 Test trước khi cài đặt

### Kiểm tra cấu hình
```
1. Mở Command Prompt
2. Chuyển đến thư mục chứa file exe
3. Chạy: LSB.LogMonitor.Service.exe --test
4. Kiểm tra có lỗi gì không
```

---

## ⚠️ Yêu cầu hệ thống

- Windows 10/11 hoặc Windows Server 2016+
- .NET 8.0 Runtime (tải tại: https://dotnet.microsoft.com/download/dotnet/8.0)
- Quyền Administrator để cài đặt service
- Kết nối Internet để gửi Telegram

---

## 🚨 Xử lý lỗi

### Lỗi "Access Denied"
```
Giải pháp: Chạy script với quyền Administrator
- Click chuột phải → "Run as administrator"
```

### Service không khởi động
```
1. Kiểm tra Windows Event Viewer
2. Kiểm tra đường dẫn logs có tồn tại không
3. Kiểm tra Telegram Bot Token có đúng không
4. Chạy test mode để kiểm tra cấu hình
```

### Không nhận được thông báo Telegram
```
1. Kiểm tra Bot Token và Chat ID
2. Kiểm tra kết nối Internet
3. Chạy test mode: LSB.LogMonitor.Service.exe --test
```

---

## 📞 Hỗ trợ

Nếu gặp vấn đề, vui lòng:
1. Chạy test mode và chụp màn hình lỗi
2. Kiểm tra Windows Event Viewer
3. Liên hệ để được hỗ trợ

---

## 📋 Checklist hoàn thành

- [ ] Giải nén file ZIP
- [ ] Cấu hình appsettings.json
- [ ] Chạy test mode (tùy chọn)
- [ ] Cài đặt service bằng StartService.bat
- [ ] Kiểm tra service đang chạy
- [ ] Nhận được thông báo test từ Telegram

🎉 **Cài đặt thành công!** Service sẽ tự động giám sát và gửi thông báo.
