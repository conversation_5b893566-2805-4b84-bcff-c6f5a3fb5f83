<?xml version="1.0" encoding="UTF-8"?>
<Wix xmlns="http://schemas.microsoft.com/wix/2006/wi"
     xmlns:util="http://schemas.microsoft.com/wix/UtilExtension">
    <?define LSB.LogMonitor.Service_TargetDir=$(var.LSB.LogMonitor.Service.TargetDir)?>

    <Product Id="*"
             Name="LSB Log Monitor Service"
             Language="1033"
             Codepage="1252"
             Version="1.0.0.0"
             Manufacturer="LSB"
             UpgradeCode="{EB8A2174-7977-45CB-8EDB-936FBC25378E}">
        
        <Package InstallerVersion="200"
                 Compressed="yes"
                 InstallScope="perMachine"
                 Platform="x64" />
        
        <Media Id="1" Cabinet="files.cab" EmbedCab="yes" />

        <!-- AUTO UPDATE SUPPORT -->
        <MajorUpgrade DowngradeErrorMessage="A newer version is already installed."
                      Schedule="afterInstallInitialize" />

        <!-- Directory Structure -->
        <Directory Id="TARGETDIR" Name="SourceDir">
            <Directory Id="ProgramFiles64Folder">
                <Directory Id="CompanyFolder" Name="LSB">
                    <Directory Id="INSTALLFOLDER" Name="LogMonitorService" />
                </Directory>
            </Directory>
            
            <!-- Create ProgramData directory for config -->
            <Directory Id="CommonAppDataFolder">
                <Directory Id="ProgramDataLSBFolder" Name="LSB">
                    <Component Id="CreateConfigDir" Guid="{F1234567-1234-1234-1234-123456789012}">
                        <CreateFolder />
                    </Component>
                </Directory>
            </Directory>
        </Directory>

        <!-- Features -->
        <Feature Id="Complete" Level="1" Title="LSB Log Monitor Service" Description="Complete installation">
            <ComponentGroupRef Id="HarvestedFiles" />
            <ComponentRef Id="CreateConfigDir" />
        </Feature>

        <!-- Custom Actions for Service Configuration -->
        <CustomAction Id="SetServiceTimeout"
                      Directory="INSTALLFOLDER"
                      ExeCommand='reg add "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control" /v "ServicesPipeTimeout" /t REG_DWORD /d "180000" /f'
                      Execute="deferred"
                      Impersonate="no" />

        <CustomAction Id="SetServiceConfig"
                      Directory="INSTALLFOLDER"
                      ExeCommand='sc config LSBLogMonitorService start= auto obj= LocalSystem'
                      Execute="deferred"
                      Impersonate="no" />

        <CustomAction Id="SetServiceFailureActions"
                      Directory="INSTALLFOLDER"
                      ExeCommand='sc failure LSBLogMonitorService reset= 86400 actions= restart/30000/restart/60000/restart/60000'
                      Execute="deferred"
                      Impersonate="no" />

        <CustomAction Id="StartService"
                      Directory="INSTALLFOLDER"
                      ExeCommand='sc start LSBLogMonitorService'
                      Execute="deferred"
                      Impersonate="no" />

        <!-- Install Sequence -->
        <InstallExecuteSequence>
            <Custom Action="SetServiceTimeout" After="InstallServices">NOT Installed</Custom>
            <Custom Action="SetServiceConfig" After="SetServiceTimeout">NOT Installed</Custom>
            <Custom Action="SetServiceFailureActions" After="SetServiceConfig">NOT Installed</Custom>
            <Custom Action="StartService" After="SetServiceFailureActions">NOT Installed</Custom>
        </InstallExecuteSequence>

        <!-- UI -->
        <UIRef Id="WixUI_Minimal" />
        <WixVariable Id="WixUILicenseRtf" Value="License.rtf" />
        
    </Product>
</Wix>
