import os
import shutil
import zipfile
from pathlib import Path

def create_installer_zip():
    print("Creating LSB.LogMonitor.Service.Installer.zip...")
    
    # Paths
    base_dir = Path(".")
    release_dir = base_dir / "LSB.LogMonitor.Service" / "bin" / "Release" / "net8.0"
    scripts_dir = base_dir / "LSB.LogMonitor.Service" / "Scripts"
    temp_dir = base_dir / "LSB.LogMonitor.Service.Installer.temp"
    zip_file = base_dir / "LSB.LogMonitor.Service.Installer.zip"
    
    # Clean up temp directory
    if temp_dir.exists():
        shutil.rmtree(temp_dir)
    temp_dir.mkdir()
    
    # Files to exclude
    exclude_files = {
        "appsettings.json",
        "appsettings.Development.json"
    }
    exclude_extensions = {".pdb"}
    
    # Copy files from release directory
    if release_dir.exists():
        for item in release_dir.iterdir():
            if item.name not in exclude_files and item.suffix not in exclude_extensions:
                if item.is_file():
                    shutil.copy2(item, temp_dir / item.name)
                elif item.is_dir():
                    shutil.copytree(item, temp_dir / item.name)
        print(f"Copied files from {release_dir}")
    
    # Copy scripts
    if scripts_dir.exists():
        for script in scripts_dir.glob("*.bat"):
            shutil.copy2(script, temp_dir / script.name)
        print(f"Copied scripts from {scripts_dir}")
    
    # Copy template files
    template_files = ["appsettings.template.json", "README.txt"]
    for template in template_files:
        template_path = base_dir / template
        if template_path.exists():
            shutil.copy2(template_path, temp_dir / template)
            print(f"Copied {template}")
    
    # Create ZIP file
    with zipfile.ZipFile(zip_file, 'w', zipfile.ZIP_DEFLATED) as zf:
        for file_path in temp_dir.rglob('*'):
            if file_path.is_file():
                arcname = file_path.relative_to(temp_dir)
                zf.write(file_path, arcname)
    
    # Clean up temp directory
    shutil.rmtree(temp_dir)
    
    # Show result
    if zip_file.exists():
        size_kb = zip_file.stat().st_size / 1024
        print(f"\nZIP Created Successfully!")
        print(f"File: {zip_file.name}")
        print(f"Size: {size_kb:.2f} KB")
        
        # List contents
        print(f"\nContents:")
        with zipfile.ZipFile(zip_file, 'r') as zf:
            for name in sorted(zf.namelist()):
                print(f"  {name}")
    else:
        print("Failed to create ZIP file")

if __name__ == "__main__":
    create_installer_zip()
