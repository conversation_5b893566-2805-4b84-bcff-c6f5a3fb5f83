@echo off
echo Testing WiX Installer Build Process...
echo.

REM Check if we're in the right directory
if not exist "Product.wxs" (
    echo Error: Product.wxs not found. Please run this script from the LSB.LogMonitor.Service.Installer directory.
    pause
    exit /b 1
)

REM Step 1: Build the service project
echo Step 1: Building service project...
dotnet build ..\LSB.LogMonitor.Service -c Release
if %errorlevel% neq 0 (
    echo Error: Failed to build service project
    pause
    exit /b 1
)
echo Service project built successfully.
echo.

REM Step 2: Check WiX installation
echo Step 2: Checking WiX installation...
where candle.exe >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: WiX Toolset not found in PATH.
    echo Please install WiX Toolset v3.11 or later from https://wixtoolset.org/releases/
    echo Make sure to add WiX bin directory to your PATH environment variable.
    pause
    exit /b 1
)
echo WiX Toolset found.
echo.

REM Step 3: Check service output directory
echo Step 3: Checking service output...
set SERVICE_OUTPUT=..\LSB.LogMonitor.Service\bin\Release\net8.0
if not exist "%SERVICE_OUTPUT%\LSB.LogMonitor.Service.exe" (
    echo Error: Service executable not found at %SERVICE_OUTPUT%
    pause
    exit /b 1
)
echo Service output found.
echo.

REM Step 4: Regenerate harvested files
echo Step 4: Regenerating harvested files...
heat.exe dir "%SERVICE_OUTPUT%" -cg HarvestedFiles -gg -scom -sreg -sfrag -srd -dr INSTALLFOLDER -var "var.LSB.LogMonitor.Service.TargetDir" -out HarvestedFiles.wxs
if %errorlevel% neq 0 (
    echo Error: Failed to generate harvested files
    pause
    exit /b 1
)
echo Harvested files generated.
echo.

REM Step 5: Compile WiX files
echo Step 5: Compiling WiX files...
candle.exe -ext WixUtilExtension -ext WixUIExtension Product.wxs HarvestedFiles.wxs -dLSB.LogMonitor.Service.TargetDir="%SERVICE_OUTPUT%\"
if %errorlevel% neq 0 (
    echo Error: Failed to compile WiX files
    pause
    exit /b 1
)
echo WiX files compiled.
echo.

REM Step 6: Link MSI
echo Step 6: Creating MSI package...
light.exe -ext WixUtilExtension -ext WixUIExtension Product.wixobj HarvestedFiles.wixobj -out LSB.LogMonitor.Service.Installer.msi
if %errorlevel% neq 0 (
    echo Error: Failed to create MSI package
    pause
    exit /b 1
)
echo.

echo SUCCESS: MSI package created successfully!
echo File: LSB.LogMonitor.Service.Installer.msi
if exist "LSB.LogMonitor.Service.Installer.msi" (
    for %%A in ("LSB.LogMonitor.Service.Installer.msi") do echo Size: %%~zA bytes
)
echo.
echo You can now distribute this MSI file to install the service on other machines.
pause
