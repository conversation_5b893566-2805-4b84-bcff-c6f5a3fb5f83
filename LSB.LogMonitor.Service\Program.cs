﻿using System;
using System.IO;
using System.Reflection;
using System.Threading.Tasks;
using LSB.LogMonitor.Services;
using LSB.LogMonitor.Service.Configuration;
using LSB.LogMonitor.Service.Contracts;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

// Fix working directory for Windows Service
try
{
    string exePath = Assembly.GetExecutingAssembly().Location;
    string exeDir = Path.GetDirectoryName(exePath);
    Directory.SetCurrentDirectory(exeDir);
    Console.WriteLine($"Working Directory set to: {Directory.GetCurrentDirectory()}");
}
catch (Exception ex)
{
    Console.WriteLine($"Warning: Could not set working directory: {ex.Message}");
}

// Console Application - can run directly or be installed as Windows Service
var builder = Host.CreateDefaultBuilder(args)
    .ConfigureAppConfiguration((context, config) =>
    {
        // Add configuration loading
        config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
        config.AddJsonFile($"appsettings.{context.HostingEnvironment.EnvironmentName}.json", optional: true);

        // Priority: Environment Variables override JSON config (for security)
        config.AddEnvironmentVariables("LSB_");
        config.AddEnvironmentVariables();
    })
    .ConfigureServices((context, services) =>
    {
        // Configure options
        services.Configure<LogMonitorOptions>(context.Configuration.GetSection(LogMonitorOptions.SectionName));

        services.AddSingleton<IConfigService, ConfigService>();
        services.AddSingleton<ILogService, LogService>();
        services.AddSingleton<ITelegramService, TelegramService>();
        services.AddHostedService<LogMonitorWorker>();
    })
    .ConfigureLogging(logging =>
    {
        logging.ClearProviders();
        logging.AddConsole(); // Console logging
        logging.SetMinimumLevel(LogLevel.Information);
    });

// Service runs directly without test mode

var host = builder.Build();
Console.WriteLine("LSB Telemetry Service starting...");

try
{
    // Important: Use RunAsync instead of Run to prevent blocking
    await host.RunAsync();
}
catch (Exception ex)
{
    Console.WriteLine($"Service error: {ex.Message}");
    throw;
}
finally
{
    Console.WriteLine("LSB Telemetry Service stopped");
}