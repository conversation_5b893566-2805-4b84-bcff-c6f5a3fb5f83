﻿using LSB.LogMonitor.Services;
using LSB.LogMonitor.Service.Configuration;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System.Reflection;
using System.IO;

// Fix working directory for Windows Service
try
{
    string exePath = Assembly.GetExecutingAssembly().Location;
    string exeDir = Path.GetDirectoryName(exePath);
    Directory.SetCurrentDirectory(exeDir);
    Console.WriteLine($"Working Directory set to: {Directory.GetCurrentDirectory()}");
}
catch (Exception ex)
{
    Console.WriteLine($"Warning: Could not set working directory: {ex.Message}");
}

// Console Application - can run directly or be installed as Windows Service
var builder = new HostBuilder()
    .ConfigureAppConfiguration((context, config) =>
    {
        // Add configuration loading
        config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
        config.AddJsonFile($"appsettings.{context.HostingEnvironment.EnvironmentName}.json", optional: true);

        // Priority: Environment Variables override JSON config (for security)
        config.AddEnvironmentVariables("LSB_");
        config.AddEnvironmentVariables();
    })
    .ConfigureServices((context, services) =>
    {
        // Configure options
        services.Configure<LogMonitorOptions>(context.Configuration.GetSection(LogMonitorOptions.SectionName));

        services.AddSingleton<IConfigService, ConfigService>();
        services.AddSingleton<ILogService, LogService>();
        services.AddSingleton<ITelegramService, TelegramService>();
        services.AddHostedService<LogMonitorWorker>();
    })
    .ConfigureLogging(logging =>
    {
        logging.ClearProviders();
        logging.AddConsole(); // Console logging
        logging.SetMinimumLevel(LogLevel.Information);
    });

// Check for test argument
if (args.Length > 0 && args[0].Equals("--test", StringComparison.OrdinalIgnoreCase))
{
    Console.WriteLine("🔧 Running in test mode...");
    try
    {
        await LSB.LogMonitor.Service.QuickTest.RunTest();
        Console.WriteLine("✅ Test completed successfully!");
    }
    catch (Exception ex)
    {
        Console.WriteLine($"❌ Test failed: {ex.Message}");
        Console.WriteLine($"Stack trace: {ex.StackTrace}");
    }
    Console.WriteLine("Press any key to exit...");
    Console.ReadKey();
    return;
}

var host = builder.Build();
Console.WriteLine("LSB Telemetry Service starting...");

try
{
    // Important: Use RunAsync instead of Run to prevent blocking
    await host.RunAsync();
}
catch (Exception ex)
{
    Console.WriteLine($"Service error: {ex.Message}");
    throw;
}
finally
{
    Console.WriteLine("LSB Telemetry Service stopped");
}