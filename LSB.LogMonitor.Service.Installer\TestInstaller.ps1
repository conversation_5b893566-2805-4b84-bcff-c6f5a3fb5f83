# Script to test the LSB Log Monitor Service Installer
param(
    [switch]$Install = $false,
    [switch]$Uninstall = $false,
    [switch]$Status = $false
)

$ServiceName = "LSBLogMonitorService"
$InstallerFile = "LSB.LogMonitor.Service.Installer.msi"

function Test-AdminRights {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

function Get-ServiceStatus {
    try {
        $service = Get-Service -Name $ServiceName -ErrorAction SilentlyContinue
        if ($service) {
            Write-Host "Service Status: $($service.Status)" -ForegroundColor Green
            Write-Host "Service Display Name: $($service.DisplayName)" -ForegroundColor Green
            Write-Host "Service Start Type: $($service.StartType)" -ForegroundColor Green
            return $true
        } else {
            Write-Host "Service not found" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "Error checking service: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Install-Service {
    if (-not (Test-Path $InstallerFile)) {
        Write-Host "Installer file not found: $InstallerFile" -ForegroundColor Red
        Write-Host "Please run BuildInstaller.ps1 first to create the MSI file." -ForegroundColor Yellow
        return $false
    }

    if (-not (Test-AdminRights)) {
        Write-Host "Administrator rights required for installation" -ForegroundColor Red
        return $false
    }

    Write-Host "Installing LSB Log Monitor Service..." -ForegroundColor Cyan
    try {
        $process = Start-Process -FilePath "msiexec.exe" -ArgumentList "/i", $InstallerFile, "/quiet" -Wait -PassThru
        if ($process.ExitCode -eq 0) {
            Write-Host "Installation completed successfully" -ForegroundColor Green
            Start-Sleep -Seconds 3
            return Get-ServiceStatus
        } else {
            Write-Host "Installation failed with exit code: $($process.ExitCode)" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "Installation error: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Uninstall-Service {
    if (-not (Test-AdminRights)) {
        Write-Host "Administrator rights required for uninstallation" -ForegroundColor Red
        return $false
    }

    Write-Host "Uninstalling LSB Log Monitor Service..." -ForegroundColor Cyan
    try {
        $process = Start-Process -FilePath "msiexec.exe" -ArgumentList "/x", $InstallerFile, "/quiet" -Wait -PassThru
        if ($process.ExitCode -eq 0) {
            Write-Host "Uninstallation completed successfully" -ForegroundColor Green
            return $true
        } else {
            Write-Host "Uninstallation failed with exit code: $($process.ExitCode)" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "Uninstallation error: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Main execution
Write-Host "LSB Log Monitor Service Installer Test" -ForegroundColor Yellow
Write-Host "=======================================" -ForegroundColor Yellow

if ($Install) {
    Install-Service
} elseif ($Uninstall) {
    Uninstall-Service
} elseif ($Status) {
    Get-ServiceStatus
} else {
    Write-Host "Usage:" -ForegroundColor Cyan
    Write-Host "  .\TestInstaller.ps1 -Install    # Install the service" -ForegroundColor White
    Write-Host "  .\TestInstaller.ps1 -Uninstall  # Uninstall the service" -ForegroundColor White
    Write-Host "  .\TestInstaller.ps1 -Status     # Check service status" -ForegroundColor White
    Write-Host ""
    Write-Host "Current status:" -ForegroundColor Cyan
    Get-ServiceStatus
}
