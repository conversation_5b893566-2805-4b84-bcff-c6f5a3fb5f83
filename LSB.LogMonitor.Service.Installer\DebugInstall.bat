@echo off
echo Debug Installation Script for LSB Log Monitor Service
echo ====================================================

REM Check if running as Administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script must be run as Administrator
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

echo Running as Administrator - OK
echo.

REM Check if MSI file exists
if not exist "LSB.LogMonitor.Service.Installer.msi" (
    echo ERROR: MSI file not found
    echo Please make sure LSB.LogMonitor.Service.Installer.msi is in the current directory
    pause
    exit /b 1
)

echo MSI file found - OK
echo.

REM Stop and remove existing service if any
echo Checking for existing service...
sc query LSBLogMonitorService >nul 2>&1
if %errorLevel% equ 0 (
    echo Found existing service, stopping and removing...
    sc stop LSBLogMonitorService
    timeout /t 3 /nobreak >nul
    sc delete LSBLogMonitorService
    timeout /t 2 /nobreak >nul
    echo Existing service removed
) else (
    echo No existing service found - OK
)
echo.

REM Install with detailed logging
echo Installing with detailed logging...
echo This may take a few minutes...
msiexec /i "LSB.LogMonitor.Service.Installer.msi" /l*v "install_debug.log" /qb

if %errorLevel% equ 0 (
    echo.
    echo Installation completed successfully!
    echo.
    echo Checking service status...
    sc query LSBLogMonitorService
    echo.
    echo Service should be running now.
) else (
    echo.
    echo Installation FAILED with error code: %errorLevel%
    echo.
    echo Please check the log file: install_debug.log
    echo Common error codes:
    echo   1603 = Fatal error during installation
    echo   1618 = Another installation is already in progress
    echo   1633 = This installation package is not supported on this platform
    echo.
)

echo.
echo Press any key to view the installation log...
pause >nul
notepad install_debug.log

pause
