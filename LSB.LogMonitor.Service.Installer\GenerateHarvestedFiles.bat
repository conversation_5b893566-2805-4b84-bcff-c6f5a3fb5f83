@echo off
echo Generating HarvestedFiles.wxs using WiX Heat tool...

REM Set paths
set SERVICE_PROJECT_DIR=..\LSB.LogMonitor.Service
set OUTPUT_DIR=%SERVICE_PROJECT_DIR%\bin\Release\net8.0
set HARVEST_FILE=HarvestedFiles.wxs

REM Check if output directory exists
if not exist "%OUTPUT_DIR%" (
    echo Error: Output directory does not exist: %OUTPUT_DIR%
    echo Please build the LSB.LogMonitor.Service project in Release mode first.
    pause
    exit /b 1
)

REM Check if WiX Heat tool is available
where heat.exe >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: WiX Heat tool not found in PATH.
    echo Please install WiX Toolset v3.11 or later.
    echo Download from: https://wixtoolset.org/releases/
    pause
    exit /b 1
)

echo Building service project first...
dotnet build "%SERVICE_PROJECT_DIR%" -c Release

if %errorlevel% neq 0 (
    echo Error: Failed to build service project.
    pause
    exit /b 1
)

echo Running Heat tool to harvest files...
heat.exe dir "%OUTPUT_DIR%" -cg HarvestedFiles -gg -scom -sreg -sfrag -srd -dr INSTALLFOLDER -var "var.LSB.LogMonitor.Service.TargetDir" -out "%HARVEST_FILE%"

if %errorlevel% equ 0 (
    echo Successfully generated %HARVEST_FILE%
    echo.
    echo Next steps:
    echo 1. Review the generated %HARVEST_FILE%
    echo 2. Add ServiceInstall and ServiceControl elements to the main executable component
    echo 3. Build the installer project
) else (
    echo Error: Failed to generate %HARVEST_FILE%
)

pause
