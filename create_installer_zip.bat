@echo off
echo Creating LSB.LogMonitor.Service.Installer.zip...

REM Create temp directory
if exist "LSB.LogMonitor.Service.Installer.temp" rmdir /s /q "LSB.LogMonitor.Service.Installer.temp"
mkdir "LSB.LogMonitor.Service.Installer.temp"

REM Copy executable and dependencies (exclude sensitive files)
xcopy "LSB.LogMonitor.Service\bin\Release\net8.0\*" "LSB.LogMonitor.Service.Installer.temp\" /E /Y /EXCLUDE:exclude_list.txt

REM Copy utility scripts
xcopy "LSB.LogMonitor.Service\Scripts\*" "LSB.LogMonitor.Service.Installer.temp\" /Y

REM Copy template files
copy "appsettings.template.json" "LSB.LogMonitor.Service.Installer.temp\"
copy "README.txt" "LSB.LogMonitor.Service.Installer.temp\"

REM Create ZIP using PowerShell
powershell -Command "Compress-Archive -Path 'LSB.LogMonitor.Service.Installer.temp\*' -DestinationPath 'LSB.LogMonitor.Service.Installer.zip' -Force"

REM Cleanup
rmdir /s /q "LSB.LogMonitor.Service.Installer.temp"

echo ZIP file created: LSB.LogMonitor.Service.Installer.zip
dir "LSB.LogMonitor.Service.Installer.zip"
pause
