@echo off
echo Simple WiX Build Test...

REM Check if service exe exists
set SERVICE_DIR=..\LSB.LogMonitor.Service\bin\Release\net8.0
if not exist "%SERVICE_DIR%\LSB.LogMonitor.Service.exe" (
    echo Service executable not found. Please build the service project first.
    echo Expected location: %SERVICE_DIR%\LSB.LogMonitor.Service.exe
    pause
    exit /b 1
)

echo Service executable found.

REM Check WiX tools
where candle.exe >nul 2>&1
if %errorlevel% neq 0 (
    echo WiX candle.exe not found in PATH
    pause
    exit /b 1
)

where light.exe >nul 2>&1
if %errorlevel% neq 0 (
    echo WiX light.exe not found in PATH
    pause
    exit /b 1
)

echo WiX tools found.

REM Generate harvested files
echo Generating harvested files...
heat.exe dir "%SERVICE_DIR%" -cg HarvestedFiles -gg -scom -sreg -sfrag -srd -dr INSTALLFOLDER -var "var.LSB.LogMonitor.Service.TargetDir" -out HarvestedFiles.wxs

if %errorlevel% neq 0 (
    echo Failed to generate harvested files
    pause
    exit /b 1
)

echo Harvested files generated successfully.

REM Compile
echo Compiling WiX files...
candle.exe -ext WixUtilExtension -ext WixUIExtension Product.wxs HarvestedFiles.wxs -dLSB.LogMonitor.Service.TargetDir="%SERVICE_DIR%\"

if %errorlevel% neq 0 (
    echo Failed to compile WiX files
    pause
    exit /b 1
)

echo WiX files compiled successfully.

REM Link
echo Creating MSI...
light.exe -ext WixUtilExtension -ext WixUIExtension Product.wixobj HarvestedFiles.wixobj -out LSB.LogMonitor.Service.Installer.msi

if %errorlevel% neq 0 (
    echo Failed to create MSI
    pause
    exit /b 1
)

echo.
echo SUCCESS! MSI created: LSB.LogMonitor.Service.Installer.msi
pause
