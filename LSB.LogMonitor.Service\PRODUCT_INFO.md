# 🏢 LSB Telemetry Service - Product Information

## 📋 Product Details

- **Organization:** LSB
- **Product Name:** LSB Telemetry Service  
- **Service Name:** LSBTelemetryService
- **Display Name:** LSB Telemetry Service
- **Description:** LSB Telemetry Service - System monitoring and error notifications

## 🔄 Updated Components

### ✅ Service Configuration:
- Service Name: `LSBTelemetryService` (was: LSBLogMonitorService)
- Display Name: `LSB Telemetry Service` (was: LSB Log Monitor Service)
- Description: Updated to reflect new product name

### ✅ Scripts Updated:
- `StartService.bat` - Updated service name and display text
- `StopService.bat` - Updated service name and display text  
- `UninstallService.bat` - Updated service name and display text
- `SetupConfig.bat` - Updated header text

### ✅ Documentation Updated:
- `README.md` - Updated product name throughout
- `SECURITY_GUIDE.md` - Updated product name
- Project file metadata updated

### ✅ Assembly Information:
- AssemblyTitle: LSB Telemetry Service
- Product: LSB Telemetry Service
- Company: LSB
- Description: LSB Telemetry Service - System monitoring and error notifications

## 🚀 Deployment

When deploying this updated version:

1. **New Installations:** Will use the new service name `LSBTelemetryService`
2. **Existing Installations:** May need to uninstall old service first:
   ```cmd
   # Stop and remove old service (if exists)
   sc stop LSBLogMonitorService
   sc delete LSBLogMonitorService
   
   # Install new service
   Scripts\StartService.bat
   ```

## 📦 Package Naming

Recommended package naming convention:
- `LSB.Telemetry.Service_YYYY-MM-DD_HH-mm-ss.zip`
- Example: `LSB.Telemetry.Service_2025-06-03_13-00-00.zip`

## 🔍 Service Management

### Check Service Status:
```cmd
sc query LSBTelemetryService
```

### Service Details:
```cmd
sc qc LSBTelemetryService
```

### Windows Services Manager:
Look for "LSB Telemetry Service" in services.msc

## 📝 Notes

- All references to "Log Monitor" have been updated to "Telemetry"
- Service functionality remains the same - only naming has changed
- Backward compatibility: Old service name will not conflict with new one
- Event Log entries will show under "LSB Telemetry Service"
