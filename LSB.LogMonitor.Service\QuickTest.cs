using System;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using LSB.LogMonitor.Services;
using LSB.LogMonitor.Service.Configuration;
using LSB.LogMonitor.Service.Contracts;

namespace LSB.LogMonitor.Service
{
    public static class QuickTest
    {
        public static async Task RunTest()
        {
            Console.WriteLine("🔧 LSB Telemetry Service - Quick Test");
            Console.WriteLine("=====================================");
            Console.WriteLine();

            // Test 1: Configuration Loading
            Console.WriteLine("1. Testing configuration loading...");
            try
            {
                var config = new ConfigurationBuilder()
                    .SetBasePath(Directory.GetCurrentDirectory())
                    .AddJsonFile("appsettings.json", optional: false)
                    .Build();

                var botToken = config.GetValue<string>("Telegram:BotToken");
                var mainChatId = config.GetValue<string>("Telegram:MainChatId");
                var logPath = config.GetValue<string>("LogMonitor:LogRootPath");

                Console.WriteLine($"   ✅ Configuration loaded successfully");
                Console.WriteLine($"   📁 Log Path: {logPath}");
                Console.WriteLine($"   🤖 Bot Token: {(string.IsNullOrEmpty(botToken) ? "❌ NOT SET" : "✅ SET")}");
                Console.WriteLine($"   💬 Main Chat ID: {(string.IsNullOrEmpty(mainChatId) ? "❌ NOT SET" : "✅ SET")}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ Configuration error: {ex.Message}");
                return;
            }

            Console.WriteLine();

            // Test 2: Service Registration
            Console.WriteLine("2. Testing service registration...");
            try
            {
                var services = new ServiceCollection();
                var config = new ConfigurationBuilder()
                    .SetBasePath(Directory.GetCurrentDirectory())
                    .AddJsonFile("appsettings.json", optional: false)
                    .Build();

                services.AddSingleton<IConfiguration>(config);
                services.Configure<LogMonitorOptions>(config.GetSection(LogMonitorOptions.SectionName));
                services.AddSingleton<IConfigService, ConfigService>();
                services.AddSingleton<ILogService, LogService>();
                services.AddSingleton<ITelegramService, TelegramService>();
                services.AddLogging(builder => builder.AddConsole());

                var serviceProvider = services.BuildServiceProvider();

                var configService = serviceProvider.GetService<IConfigService>();
                var logService = serviceProvider.GetService<ILogService>();
                var telegramService = serviceProvider.GetService<ITelegramService>();

                Console.WriteLine($"   ✅ Services registered successfully");
                Console.WriteLine($"   🔧 ConfigService: {(configService != null ? "✅ OK" : "❌ FAILED")}");
                Console.WriteLine($"   📝 LogService: {(logService != null ? "✅ OK" : "❌ FAILED")}");
                Console.WriteLine($"   📱 TelegramService: {(telegramService != null ? "✅ OK" : "❌ FAILED")}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ Service registration error: {ex.Message}");
                return;
            }

            Console.WriteLine();

            // Test 3: Config File Access
            Console.WriteLine("3. Testing config file access...");
            try
            {
                var services = new ServiceCollection();
                var config = new ConfigurationBuilder()
                    .SetBasePath(Directory.GetCurrentDirectory())
                    .AddJsonFile("appsettings.json", optional: false)
                    .Build();

                services.AddSingleton<IConfiguration>(config);
                services.AddSingleton<IConfigService, ConfigService>();
                services.AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Warning));

                var serviceProvider = services.BuildServiceProvider();
                var configService = serviceProvider.GetService<IConfigService>();

                if (configService != null)
                {
                    var accName = await configService.GetAccNameAsync();
                    Console.WriteLine($"   ✅ Config service working");
                    Console.WriteLine($"   🏢 Account Name: {accName}");
                }
                else
                {
                    Console.WriteLine($"   ❌ Config service failed");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ Config file error: {ex.Message}");
            }

            Console.WriteLine();

            // Test 4: Log Directory Access
            Console.WriteLine("4. Testing log directory access...");
            try
            {
                var config = new ConfigurationBuilder()
                    .SetBasePath(Directory.GetCurrentDirectory())
                    .AddJsonFile("appsettings.json", optional: false)
                    .Build();

                var logPath = config.GetValue<string>("LogMonitor:LogRootPath");
                
                if (!string.IsNullOrEmpty(logPath))
                {
                    if (Directory.Exists(logPath))
                    {
                        var logFiles = Directory.GetFiles(logPath, "*.log");
                        Console.WriteLine($"   ✅ Log directory accessible");
                        Console.WriteLine($"   📁 Path: {logPath}");
                        Console.WriteLine($"   📄 Log files found: {logFiles.Length}");
                        
                        if (logFiles.Length > 0)
                        {
                            Console.WriteLine($"   📝 Sample files: {string.Join(", ", logFiles.Take(3).Select(Path.GetFileName))}");
                        }
                    }
                    else
                    {
                        Console.WriteLine($"   ⚠️  Log directory does not exist: {logPath}");
                        Console.WriteLine($"   💡 This is normal if no logs have been generated yet");
                    }
                }
                else
                {
                    Console.WriteLine($"   ❌ Log path not configured");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ Log directory error: {ex.Message}");
            }

            Console.WriteLine();

            // Test 5: Telegram Configuration
            Console.WriteLine("5. Testing Telegram configuration...");
            try
            {
                var config = new ConfigurationBuilder()
                    .SetBasePath(Directory.GetCurrentDirectory())
                    .AddJsonFile("appsettings.json", optional: false)
                    .Build();

                var botToken = config.GetValue<string>("Telegram:BotToken");
                var mainChatId = config.GetValue<string>("Telegram:MainChatId");
                var enabled = config.GetValue<bool>("Telegram:Enabled", true);

                Console.WriteLine($"   📱 Telegram Enabled: {(enabled ? "✅ YES" : "❌ NO")}");
                
                if (!string.IsNullOrEmpty(botToken) && !botToken.Contains("YOUR_BOT_TOKEN"))
                {
                    Console.WriteLine($"   🤖 Bot Token: ✅ Configured (length: {botToken.Length})");
                }
                else
                {
                    Console.WriteLine($"   🤖 Bot Token: ❌ Not configured or using placeholder");
                }

                if (!string.IsNullOrEmpty(mainChatId) && !mainChatId.Contains("YOUR_MAIN_CHAT_ID"))
                {
                    Console.WriteLine($"   💬 Main Chat ID: ✅ Configured");
                }
                else
                {
                    Console.WriteLine($"   💬 Main Chat ID: ❌ Not configured or using placeholder");
                }

                // Check client channels
                var clientChannels = config.GetSection("Telegram:ClientChannels").GetChildren();
                var channelCount = clientChannels.Count();
                Console.WriteLine($"   📢 Client Channels: {channelCount} configured");

                foreach (var channel in clientChannels.Take(3))
                {
                    var isConfigured = !string.IsNullOrEmpty(channel.Value) && !channel.Value.Contains("CHAT_ID");
                    Console.WriteLine($"      - {channel.Key}: {(isConfigured ? "✅" : "❌")}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ Telegram configuration error: {ex.Message}");
            }

            Console.WriteLine();
            Console.WriteLine("=====================================");
            Console.WriteLine("🎯 Test Summary:");
            Console.WriteLine("✅ If all tests show ✅, the service should work correctly");
            Console.WriteLine("❌ If any tests show ❌, please fix the configuration");
            Console.WriteLine("⚠️  Warnings are usually OK and don't prevent operation");
            Console.WriteLine();
            Console.WriteLine("💡 Next steps:");
            Console.WriteLine("1. Fix any ❌ issues shown above");
            Console.WriteLine("2. Run Scripts\\StartService.bat as Administrator");
            Console.WriteLine("3. Check Windows Event Viewer for service logs");
            Console.WriteLine("=====================================");
        }
    }
}
