@echo off
echo =======================================
echo LSB Log Monitor Service - STOP SERVICE
echo =======================================

REM Check if running as Administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script must be run as Administrator
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

echo Running as Administrator - OK
echo.

REM Set service configuration
set SERVICE_NAME=LSBLogMonitorService

echo Service Name: %SERVICE_NAME%
echo.

REM Check if service exists
sc query "%SERVICE_NAME%" >nul 2>&1
if %errorLevel% neq 0 (
    echo Service "%SERVICE_NAME%" does not exist.
    echo Nothing to stop.
    pause
    exit /b 0
)

echo Service exists. Checking status...
echo.

REM Check current service status
sc query "%SERVICE_NAME%"
echo.

REM Check if service is running
sc query "%SERVICE_NAME%" | find "RUNNING" >nul
if %errorLevel% neq 0 (
    echo Service is not running.
    echo Nothing to stop.
    pause
    exit /b 0
)

echo Service is running. Stopping service...
echo.

REM Stop the service
sc stop "%SERVICE_NAME%"

if %errorLevel% EQU 0 (
    echo ✓ Stop command sent successfully!
    echo.
    echo Waiting for service to stop...
    
    REM Wait for service to stop (max 30 seconds)
    set /a counter=0
    :wait_loop
    timeout /t 2 /nobreak >nul
    set /a counter+=2
    
    sc query "%SERVICE_NAME%" | find "STOPPED" >nul
    if %errorLevel% EQU 0 (
        echo ✓ Service stopped successfully!
        echo.
        sc query "%SERVICE_NAME%"
        echo.
        echo =======================================
        echo SERVICE STOPPED SUCCESSFULLY!
        echo =======================================
        echo.
        echo The LSB Log Monitor Service has been stopped.
        echo.
        echo To start the service again, run: StartService.bat
        echo To uninstall the service, run: UninstallService.bat
        echo.
        pause
        exit /b 0
    )
    
    if %counter% LSS 30 (
        echo Still stopping... (%counter%s)
        goto wait_loop
    )
    
    echo WARNING: Service may still be stopping...
    echo Current status:
    sc query "%SERVICE_NAME%"
    echo.
    echo If the service doesn't stop, you may need to:
    echo 1. Wait a bit longer
    echo 2. Check Task Manager for the process
    echo 3. Restart the computer if necessary
    
) else (
    echo ✗ Failed to stop service. Error code: %errorLevel%
    echo.
    echo Current service status:
    sc query "%SERVICE_NAME%"
    echo.
    echo You can try:
    echo 1. Using Services.msc to stop the service manually
    echo 2. Restarting the computer
    echo 3. Checking Windows Event Viewer for error details
)

echo.
pause
