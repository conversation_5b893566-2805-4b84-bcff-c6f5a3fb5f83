# 🛡️ SECURITY GUIDE - LSB Telemetry Service

## ⚠️ SECURITY WARNING

The `appsettings.json` file contains sensitive information:
- **Telegram Bot Token** - Can control the bot
- **Chat IDs** - Can send messages to channels
- **System paths** - Server structure information

## 🚨 RISKS WHEN INFORMATION IS EXPOSED

### Bot Token exposure:
- Hackers can send spam to channels
- Read/delete/modify messages
- Kick/ban members
- Extract sensitive information

### Chat IDs exposure:
- Know which channels are being monitored
- Customer information
- System structure

## 🛡️ PROTECTION METHODS

### Method 1: Use Template (Recommended)

1. **Never share appsettings.json with real information**
2. **Use appsettings.template.json** to send to customers
3. **Customers fill in information** into template

### Method 2: Setup Script

1. **Run Scripts\SetupConfig.bat**
2. **Enter information via command line**
3. **<PERSON><PERSON><PERSON> automatically creates appsettings.json**

### Method 3: Environment Variables

Instead of storing in JSON file, use environment variables:

```cmd
# Set environment variables
set LSB_Telegram__BotToken=YOUR_BOT_TOKEN
set LSB_Telegram__MainChatId=YOUR_CHAT_ID
set LSB_Telegram__ClientChannels__Client1=CHAT_ID_1
```

### Method 4: Encrypted Config

Encrypt sensitive information in config file.

## 📋 SECURITY CHECKLIST

### ✅ Before sending to customers:
- [ ] Remove all real Bot Tokens
- [ ] Remove all real Chat IDs
- [ ] Replace with placeholder text
- [ ] Check for no sensitive information

### ✅ Customer guidance:
- [ ] Explain the importance of security
- [ ] Guide how to fill information safely
- [ ] Warn not to share config files
- [ ] Guide safe backup procedures

### ✅ After deployment:
- [ ] Check config file on server
- [ ] Ensure restricted file access permissions
- [ ] Backup config file safely
- [ ] Monitor logs to detect unusual access

## 🔒 BEST PRACTICES

1. **Principle of Least Privilege**
   - Grant only minimum necessary permissions
   - Limit who can access config

2. **Defense in Depth**
   - Multiple security layers
   - Don't rely on a single method

3. **Regular Security Review**
   - Periodic security checks
   - Rotate Bot Token if needed

4. **Incident Response Plan**
   - Plan for handling information exposure
   - How to quickly change Bot Token

## 🚨 WHEN INFORMATION IS EXPOSED

### Immediately:
1. **Revoke Bot Token** on BotFather
2. **Create new Bot Token**
3. **Update config** on all servers
4. **Restart service**
5. **Monitor** unusual activity

### Investigation:
1. **Determine** scope of exposure
2. **Find out** the cause
3. **Improve** security procedures
4. **Train** team on security

## 📞 CONTACT

If you discover security issues, contact immediately:
- Email: [<EMAIL>]
- Phone: [Emergency number]
- Slack: [Security channel]

---

**⚠️ NOTE: Security is everyone's responsibility!**
