# 🛡️ HƯỚNG DẪN BẢO MẬT - LSB Telemetry Service

## ⚠️ CẢNH BÁO BẢO MẬT

File `appsettings.json` chứa thông tin nhạy cảm:
- **Telegram Bot Token** - <PERSON><PERSON> thể điều khiển bot
- **Chat IDs** - <PERSON><PERSON> thể gửi tin nhắn vào kênh
- **Đường dẫn hệ thống** - Thông tin cấu trúc server

## 🚨 RỦI RO KHI LỘ THÔNG TIN

### Bot Token bị lộ:
- Hacker có thể gửi spam vào kênh
- Đọc/xóa/sửa tin nhắn
- Kick/ban members
- L<PERSON><PERSON> thông tin nhạy cảm

### Chat IDs bị lộ:
- Biết được kênh nào đang monitor
- Thông tin khách hàng
- Cấu trúc hệ thống

## 🛡️ CÁCH BẢO VỆ

### Phương pháp 1: Sử dụng Template (Khuyến nghị)

1. **Không bao giờ chia sẻ appsettings.json có thông tin thật**
2. **Sử dụng appsettings.template.json** để gửi khách hàng
3. **Kh<PERSON>ch hàng tự điền thông tin** vào template

### Phương pháp 2: Setup Script

1. **Chạy Scripts\SetupConfig.bat**
2. **Nhập thông tin qua command line**
3. **Script tự tạo appsettings.json**

### Phương pháp 3: Environment Variables

Thay vì để trong file JSON, dùng biến môi trường:

```cmd
# Set environment variables
set LSB_Telegram__BotToken=YOUR_BOT_TOKEN
set LSB_Telegram__MainChatId=YOUR_CHAT_ID
set LSB_Telegram__ClientChannels__Client1=CHAT_ID_1
```

### Phương pháp 4: Encrypted Config

Mã hóa thông tin nhạy cảm trong config file.

## 📋 CHECKLIST BẢO MẬT

### ✅ Trước khi gửi khách hàng:
- [ ] Remove tất cả Bot Token thật
- [ ] Remove tất cả Chat ID thật  
- [ ] Thay bằng placeholder text
- [ ] Kiểm tra không có thông tin nhạy cảm nào

### ✅ Hướng dẫn khách hàng:
- [ ] Giải thích tầm quan trọng của bảo mật
- [ ] Hướng dẫn cách điền thông tin an toàn
- [ ] Cảnh báo không chia sẻ file config
- [ ] Hướng dẫn backup an toàn

### ✅ Sau khi deploy:
- [ ] Kiểm tra file config trên server
- [ ] Đảm bảo quyền truy cập file hạn chế
- [ ] Backup config file an toàn
- [ ] Monitor logs để phát hiện truy cập bất thường

## 🔒 BEST PRACTICES

1. **Principle of Least Privilege**
   - Chỉ cấp quyền tối thiểu cần thiết
   - Hạn chế ai có thể truy cập config

2. **Defense in Depth**
   - Nhiều lớp bảo mật
   - Không dựa vào một phương pháp duy nhất

3. **Regular Security Review**
   - Định kỳ kiểm tra bảo mật
   - Rotate Bot Token nếu cần

4. **Incident Response Plan**
   - Kế hoạch xử lý khi bị lộ thông tin
   - Cách thay đổi Bot Token nhanh chóng

## 🚨 KHI BỊ LỘ THÔNG TIN

### Ngay lập tức:
1. **Revoke Bot Token** trên BotFather
2. **Tạo Bot Token mới**
3. **Cập nhật config** trên tất cả server
4. **Restart service**
5. **Monitor** hoạt động bất thường

### Điều tra:
1. **Xác định** phạm vi bị lộ
2. **Tìm hiểu** nguyên nhân
3. **Cải thiện** quy trình bảo mật
4. **Đào tạo** team về bảo mật

## 📞 LIÊN HỆ

Nếu phát hiện vấn đề bảo mật, liên hệ ngay:
- Email: [<EMAIL>]
- Phone: [Emergency number]
- Slack: [Security channel]

---

**⚠️ LƯU Ý: Bảo mật là trách nhiệm của tất cả mọi người!**
