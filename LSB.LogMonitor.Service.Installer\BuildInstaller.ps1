# PowerShell script to build LSB Log Monitor Service Installer
param(
    [string]$Configuration = "Release",
    [switch]$Clean = $false
)

Write-Host "Building LSB Log Monitor Service Installer..." -ForegroundColor Green

# Set paths
$ServiceProjectDir = "..\LSB.LogMonitor.Service"
$InstallerProjectFile = "LSB.LogMonitor.Service.Installer.wixproj"
$OutputDir = "bin\$Configuration"

try {
    # Clean if requested
    if ($Clean) {
        Write-Host "Cleaning previous builds..." -ForegroundColor Yellow
        if (Test-Path $OutputDir) {
            Remove-Item $OutputDir -Recurse -Force
        }
        if (Test-Path "obj") {
            Remove-Item "obj" -Recurse -Force
        }
    }

    # Step 1: Build the service project
    Write-Host "Step 1: Building service project..." -ForegroundColor Cyan
    $buildResult = dotnet build $ServiceProjectDir -c $Configuration
    if ($LASTEXITCODE -ne 0) {
        throw "Failed to build service project"
    }
    Write-Host "Service project built successfully" -ForegroundColor Green

    # Step 2: Check if WiX is available
    Write-Host "Step 2: Checking WiX installation..." -ForegroundColor Cyan
    $wixPath = Get-Command "candle.exe" -ErrorAction SilentlyContinue
    if (-not $wixPath) {
        throw "WiX Toolset not found. Please install WiX Toolset v3.11 or later from https://wixtoolset.org/releases/"
    }
    Write-Host "WiX Toolset found: $($wixPath.Source)" -ForegroundColor Green

    # Step 3: Regenerate harvested files
    Write-Host "Step 3: Regenerating harvested files..." -ForegroundColor Cyan
    $serviceOutputDir = "$ServiceProjectDir\bin\$Configuration\net8.0"
    if (-not (Test-Path $serviceOutputDir)) {
        throw "Service output directory not found: $serviceOutputDir"
    }

    $heatArgs = @(
        "dir", $serviceOutputDir,
        "-cg", "HarvestedFiles",
        "-gg", "-scom", "-sreg", "-sfrag", "-srd",
        "-dr", "INSTALLFOLDER",
        "-var", "var.LSB.LogMonitor.Service.TargetDir",
        "-out", "HarvestedFiles.wxs"
    )
    
    & heat.exe $heatArgs
    if ($LASTEXITCODE -ne 0) {
        throw "Failed to generate harvested files"
    }
    Write-Host "Harvested files generated successfully" -ForegroundColor Green

    # Step 4: Build the installer
    Write-Host "Step 4: Building installer..." -ForegroundColor Cyan
    
    # Use MSBuild to build the WiX project
    $msbuildArgs = @(
        $InstallerProjectFile,
        "/p:Configuration=$Configuration",
        "/p:LSB.LogMonitor.Service.TargetDir=$serviceOutputDir\"
    )
    
    & msbuild.exe $msbuildArgs
    if ($LASTEXITCODE -ne 0) {
        throw "Failed to build installer"
    }

    # Step 5: Show results
    Write-Host "Step 5: Build completed successfully!" -ForegroundColor Green
    $msiFile = Get-ChildItem "$OutputDir\*.msi" | Select-Object -First 1
    if ($msiFile) {
        Write-Host "Installer created: $($msiFile.FullName)" -ForegroundColor Yellow
        Write-Host "File size: $([math]::Round($msiFile.Length / 1MB, 2)) MB" -ForegroundColor Yellow
    }

} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "`nInstaller build completed successfully!" -ForegroundColor Green
Write-Host "You can now distribute the MSI file to install the service on other machines." -ForegroundColor Cyan
