# PowerShell script to build LSB Log Monitor Service Installer
param(
    [string]$Configuration = "Release",
    [switch]$Clean = $false
)

Write-Host "Building LSB Log Monitor Service Installer..." -ForegroundColor Green

# Set paths
$ServiceProjectDir = "..\LSB.LogMonitor.Service"
$InstallerProjectFile = "LSB.LogMonitor.Service.Installer.wixproj"
$OutputDir = "bin\$Configuration"

try {
    # Clean if requested
    if ($Clean) {
        Write-Host "Cleaning previous builds..." -ForegroundColor Yellow
        if (Test-Path $OutputDir) {
            Remove-Item $OutputDir -Recurse -Force
        }
        if (Test-Path "obj") {
            Remove-Item "obj" -Recurse -Force
        }
    }

    # Step 1: Check if service project is built
    Write-Host "Step 1: Checking service project..." -ForegroundColor Cyan
    $serviceOutputDir = "$ServiceProjectDir\bin\$Configuration\net8.0"
    if (-not (Test-Path "$serviceOutputDir\LSB.LogMonitor.Service.exe")) {
        Write-Host "Service executable not found. Attempting to build..." -ForegroundColor Yellow
        try {
            dotnet build $ServiceProjectDir -c $Configuration
            if ($LASTEXITCODE -ne 0) {
                throw "Failed to build service project"
            }
        } catch {
            Write-Host "Warning: Could not build service project automatically." -ForegroundColor Yellow
            Write-Host "Please build the service project manually in $Configuration mode first." -ForegroundColor Yellow
            throw "Service project not built"
        }
    }
    Write-Host "Service project files found" -ForegroundColor Green

    # Step 2: Check if WiX is available
    Write-Host "Step 2: Checking WiX installation..." -ForegroundColor Cyan
    $wixPath = Get-Command "candle.exe" -ErrorAction SilentlyContinue
    if (-not $wixPath) {
        # Try to find WiX in common installation paths
        $commonPaths = @(
            "C:\Program Files (x86)\WiX Toolset v3.14\bin",
            "C:\Program Files (x86)\WiX Toolset v3.11\bin",
            "C:\Program Files\WiX Toolset v3.14\bin",
            "C:\Program Files\WiX Toolset v3.11\bin"
        )

        $wixBinPath = $null
        foreach ($path in $commonPaths) {
            if (Test-Path "$path\candle.exe") {
                $wixBinPath = $path
                break
            }
        }

        if ($wixBinPath) {
            $env:PATH += ";$wixBinPath"
            Write-Host "WiX Toolset found at: $wixBinPath" -ForegroundColor Green
        } else {
            throw "WiX Toolset not found. Please install WiX Toolset v3.11 or later from https://wixtoolset.org/releases/"
        }
    } else {
        Write-Host "WiX Toolset found: $($wixPath.Source)" -ForegroundColor Green
    }

    # Step 3: Regenerate harvested files
    Write-Host "Step 3: Regenerating harvested files..." -ForegroundColor Cyan
    if (-not (Test-Path $serviceOutputDir)) {
        throw "Service output directory not found: $serviceOutputDir"
    }

    $heatArgs = @(
        "dir", $serviceOutputDir,
        "-cg", "HarvestedFiles",
        "-gg", "-scom", "-sreg", "-sfrag", "-srd",
        "-dr", "INSTALLFOLDER",
        "-var", "var.LSB.LogMonitor.Service.TargetDir",
        "-out", "HarvestedFiles.wxs"
    )
    
    & heat.exe $heatArgs
    if ($LASTEXITCODE -ne 0) {
        throw "Failed to generate harvested files"
    }
    Write-Host "Harvested files generated successfully" -ForegroundColor Green

    # Step 4: Build the installer
    Write-Host "Step 4: Building installer..." -ForegroundColor Cyan

    # Compile WiX files
    Write-Host "  Compiling WiX files..." -ForegroundColor Yellow
    & candle.exe -ext WixUtilExtension -ext WixUIExtension Product.wxs HarvestedFiles.wxs "-dLSB.LogMonitor.Service.TargetDir=$serviceOutputDir\"
    if ($LASTEXITCODE -ne 0) {
        throw "Failed to compile WiX files"
    }

    # Link MSI
    Write-Host "  Linking MSI package..." -ForegroundColor Yellow
    & light.exe -ext WixUtilExtension -ext WixUIExtension Product.wixobj HarvestedFiles.wixobj -out "LSB.LogMonitor.Service.Installer.msi" -sice:ICE80
    if ($LASTEXITCODE -ne 0) {
        throw "Failed to create MSI package"
    }

    # Step 5: Show results
    Write-Host "Step 5: Build completed successfully!" -ForegroundColor Green
    $msiFile = Get-ChildItem "*.msi" | Select-Object -First 1
    if ($msiFile) {
        Write-Host "Installer created: $($msiFile.FullName)" -ForegroundColor Yellow
        Write-Host "File size: $([math]::Round($msiFile.Length / 1MB, 2)) MB" -ForegroundColor Yellow
    }

} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "`nInstaller build completed successfully!" -ForegroundColor Green
Write-Host "You can now distribute the MSI file to install the service on other machines." -ForegroundColor Cyan
