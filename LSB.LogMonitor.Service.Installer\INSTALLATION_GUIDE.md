# Hướng dẫn cài đặt LSB Log Monitor Service

## Tóm tắt
LSB Log Monitor Service là một Windows Service giám sát log files và gửi thông báo qua Telegram khi phát hiện lỗi.

## Yêu cầu hệ thống
- Windows 10/11 hoặc Windows Server 2016+
- .NET 8.0 Runtime (sẽ được cài tự động nếu cần)
- Quyền Administrator để cài đặt service

## Cách cài đặt

### Bước 1: Tải file installer
- Tải file `LSB.LogMonitor.Service.Installer.msi` (khoảng 2MB)

### Bước 2: Chạy installer
1. **Click chuột phải** vào file MSI và chọn **"Run as administrator"**
2. Hoặc mở Command Prompt/PowerShell với quyền Administrator và chạy:
   ```cmd
   msiexec /i LSB.LogMonitor.Service.Installer.msi
   ```

### Bước 3: Làm theo hướng dẫn cài đặt
1. Đồng ý với License Agreement
2. Chọn thư mục cài đặt (mặc định: `C:\Program Files\LSB\LogMonitorService`)
3. Click "Install"

### Bước 4: Kiểm tra cài đặt
Sau khi cài đặt xong:
1. Mở **Services** (services.msc)
2. Tìm service **"LSB Log Monitor Service"**
3. Service sẽ tự động start và có trạng thái "Running"

## Cấu hình

### File cấu hình chính
Service sẽ đọc cấu hình từ:
- `C:\Program Files\LSB\LogMonitorService\appsettings.json`

### Cấu hình Telegram
Chỉnh sửa file `appsettings.json`:
```json
{
    "Telegram": {
        "BotToken": "YOUR_BOT_TOKEN",
        "MainChatId": "YOUR_MAIN_CHAT_ID",
        "Enabled": true,
        "ClientChannels": {
            "ClientName1": "CHAT_ID_1",
            "ClientName2": "CHAT_ID_2"
        }
    },
    "LogMonitor": {
        "CheckIntervalHours": 1.0,
        "LogRootPath": "C:\\ProgramData\\DaBox\\LSBHub\\Logs"
    }
}
```

### Cấu hình account name (tùy chọn)
Tạo file `C:\ProgramData\LSB\logmonitor.config`:
```json
{
    "AccName": "TenMayTinh"
}
```

## Quản lý Service

### Khởi động lại service
```cmd
net stop LSBLogMonitorService
net start LSBLogMonitorService
```

### Xem logs của service
- Event Viewer → Windows Logs → Application
- Tìm events từ source "LSB Log Monitor Service"

### Kiểm tra trạng thái
```cmd
sc query LSBLogMonitorService
```

## Gỡ cài đặt

### Cách 1: Qua Control Panel
1. Mở **"Add or Remove Programs"**
2. Tìm **"LSB Log Monitor Service"**
3. Click **"Uninstall"**

### Cách 2: Qua Command Line
```cmd
msiexec /x LSB.LogMonitor.Service.Installer.msi
```

## Troubleshooting

### Service không start được
1. Kiểm tra Event Viewer cho chi tiết lỗi
2. Đảm bảo .NET 8.0 Runtime đã được cài đặt
3. Kiểm tra quyền truy cập thư mục logs

### Không nhận được thông báo Telegram
1. Kiểm tra Bot Token và Chat ID trong appsettings.json
2. Đảm bảo bot đã được thêm vào group/channel
3. Kiểm tra kết nối internet

### Service bị crash
1. Xem Event Viewer cho stack trace
2. Kiểm tra file cấu hình JSON syntax
3. Restart service

## Liên hệ hỗ trợ
Nếu gặp vấn đề, vui lòng cung cấp:
- Windows version
- Event Viewer logs
- File appsettings.json (ẩn sensitive data)
- Mô tả chi tiết lỗi
