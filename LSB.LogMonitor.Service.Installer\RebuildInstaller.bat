@echo off
echo Rebuilding LSB Log Monitor Service Installer
echo ===========================================

REM Check if running as Administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script must be run as Administrator
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

echo Running as Administrator - OK
echo.

REM Clean previous builds
echo Cleaning previous builds...
if exist "bin" rmdir /s /q "bin"
if exist "obj" rmdir /s /q "obj"
if exist "*.msi" del /q "*.msi"
if exist "*.wixpdb" del /q "*.wixpdb"
echo Clean completed.
echo.

REM Check if WiX is available
where candle.exe >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: WiX Toolset not found in PATH
    echo Please install WiX Toolset v3.11 or later
    echo Download from: https://wixtoolset.org/releases/
    pause
    exit /b 1
)

echo WiX Toolset found - OK
echo.

REM Build the main service first
echo Building main service...
cd ..\LSB.LogMonitor.Service
dotnet build -c Release
if %errorLevel% neq 0 (
    echo ERROR: Failed to build main service
    pause
    exit /b 1
)
echo Main service built successfully.
echo.

REM Return to installer directory
cd ..\LSB.LogMonitor.Service.Installer

REM Harvest files
echo Harvesting files...
heat.exe dir "..\LSB.LogMonitor.Service\bin\Release\net8.0" -cg ServiceFiles -gg -scom -sreg -sfrag -srd -dr INSTALLFOLDER -out ServiceFiles.wxs
if %errorLevel% neq 0 (
    echo ERROR: Failed to harvest files
    pause
    exit /b 1
)
echo Files harvested successfully.
echo.

REM Compile WiX files
echo Compiling WiX files...
candle.exe -arch x64 Product.wxs ServiceFiles.wxs
if %errorLevel% neq 0 (
    echo ERROR: Failed to compile WiX files
    pause
    exit /b 1
)
echo WiX files compiled successfully.
echo.

REM Link MSI
echo Linking MSI...
light.exe -ext WixUIExtension -ext WixUtilExtension Product.wixobj ServiceFiles.wixobj -out LSB.LogMonitor.Service.Installer.msi
if %errorLevel% neq 0 (
    echo ERROR: Failed to link MSI
    pause
    exit /b 1
)
echo MSI linked successfully.
echo.

REM Validate MSI
echo Validating MSI...
if exist "LSB.LogMonitor.Service.Installer.msi" (
    echo MSI file created successfully: LSB.LogMonitor.Service.Installer.msi
    
    REM Get file size
    for %%A in ("LSB.LogMonitor.Service.Installer.msi") do echo File size: %%~zA bytes
    
    REM Test MSI integrity
    msiexec /a "LSB.LogMonitor.Service.Installer.msi" /qn TARGETDIR="%TEMP%\MSITest" /l*v "%TEMP%\msi_validation.log"
    if %errorLevel% equ 0 (
        echo MSI validation passed - OK
        rmdir /s /q "%TEMP%\MSITest" 2>nul
    ) else (
        echo WARNING: MSI validation failed
        echo Check log: %TEMP%\msi_validation.log
    )
) else (
    echo ERROR: MSI file was not created
    pause
    exit /b 1
)

echo.
echo ==========================================
echo BUILD COMPLETED SUCCESSFULLY!
echo ==========================================
echo.
echo MSI file: LSB.LogMonitor.Service.Installer.msi
echo.
echo To install, run as Administrator:
echo   msiexec /i LSB.LogMonitor.Service.Installer.msi /l*v install.log
echo.

pause
