# LSB Log Monitor Service Installer

Đây là WiX installer để cài đặt LSB Log Monitor Service trên các máy Windows khác.

## <PERSON><PERSON><PERSON> cầu hệ thống

- Windows 10/11 hoặc Windows Server 2016+
- .NET 8.0 Runtime (sẽ được cài tự động nếu cần)
- <PERSON><PERSON><PERSON>n Administrator để cài đặt service

## Yêu cầu để build installer

- Visual Studio 2022 với WiX Toolset v3.11+
- .NET 8.0 SDK
- WiX Toolset Extension cho Visual Studio

## Cách build installer

### Phương pháp 1: Sử dụng PowerShell Script (Khuyến nghị)

```powershell
# Chạy từ thư mục LSB.LogMonitor.Service.Installer
.\BuildInstaller.ps1 -Configuration Release
```

### Phương pháp 2: Sử dụng Visual Studio

1. Mở solution `LSB.LogMonitor.Service.sln`
2. Set configuration thành "Release"
3. Build project `LSB.LogMonitor.Service` trước
4. Build project `LSB.LogMonitor.Service.Installer`

### Phương pháp 3: Sử dụng Command Line

```cmd
# Build service project trước
dotnet build ..\LSB.LogMonitor.Service -c Release

# Regenerate harvested files
.\GenerateHarvestedFiles.bat

# Build installer
msbuild LSB.LogMonitor.Service.Installer.wixproj /p:Configuration=Release
```

## Cấu trúc installer

- **Product.wxs**: File chính định nghĩa installer
- **HarvestedFiles.wxs**: File tự động tạo chứa tất cả DLL dependencies
- **License.rtf**: License agreement
- **BuildInstaller.ps1**: Script PowerShell để build tự động
- **GenerateHarvestedFiles.bat**: Script để regenerate harvested files

## Tính năng installer

- ✅ Tự động cài đặt Windows Service
- ✅ Cấu hình service start automatically
- ✅ Cài đặt tất cả dependencies cần thiết
- ✅ Tạo thư mục config trong ProgramData
- ✅ Cấu hình service failure recovery
- ✅ Hỗ trợ upgrade/uninstall
- ✅ UI đơn giản với license agreement

## Cài đặt service

1. Chạy file MSI với quyền Administrator
2. Đồng ý license agreement
3. Chọn thư mục cài đặt (mặc định: C:\Program Files\LSB\LogMonitorService)
4. Installer sẽ tự động:
   - Copy tất cả files cần thiết
   - Cài đặt và start Windows Service
   - Cấu hình service auto-start

## Cấu hình sau khi cài đặt

Service sẽ tìm file config tại:
- `C:\ProgramData\LSB\logmonitor.config` (nếu có)
- Hoặc sử dụng `appsettings.json` trong thư mục cài đặt

## Gỡ cài đặt

- Sử dụng "Add or Remove Programs" trong Windows
- Hoặc chạy lại MSI file và chọn "Remove"

## Troubleshooting

### Lỗi "WiX Toolset not found"
- Cài đặt WiX Toolset v3.11+ từ https://wixtoolset.org/releases/
- Thêm WiX bin folder vào PATH

### Lỗi "Service failed to start"
- Kiểm tra Event Viewer cho chi tiết lỗi
- Đảm bảo .NET 8.0 Runtime đã được cài đặt
- Kiểm tra quyền truy cập thư mục logs

### Lỗi build "Target framework mismatch"
- Đảm bảo service project đã được build với .NET 8.0
- Kiểm tra đường dẫn trong Product.wxs

## Files được tạo

Sau khi build thành công, bạn sẽ có:
- `bin\Release\LSB.LogMonitor.Service.Installer.msi` - File installer chính
- `bin\Release\LSB.LogMonitor.Service.Installer.wixpdb` - Debug symbols

## Phân phối

File MSI có thể được phân phối và cài đặt trên bất kỳ máy Windows nào có:
- .NET 8.0 Runtime
- Quyền Administrator
