@echo off
echo ===========================================
echo LSB Log Monitor Service - UNINSTALL SERVICE
echo ===========================================

REM Check if running as Administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script must be run as Administrator
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

echo Running as Administrator - OK
echo.

REM Set service configuration
set SERVICE_NAME=LSBLogMonitorService

echo Service Name: %SERVICE_NAME%
echo.

REM Check if service exists
sc query "%SERVICE_NAME%" >nul 2>&1
if %errorLevel% neq 0 (
    echo Service "%SERVICE_NAME%" does not exist.
    echo Nothing to uninstall.
    pause
    exit /b 0
)

echo Service exists. Checking status...
echo.

REM Show current service status
sc query "%SERVICE_NAME%"
echo.

REM Check if service is running and stop it first
sc query "%SERVICE_NAME%" | find "RUNNING" >nul
if %errorLevel% EQU 0 (
    echo Service is running. Stopping service first...
    echo.
    
    sc stop "%SERVICE_NAME%"
    if %errorLevel% EQU 0 (
        echo ✓ Stop command sent successfully!
        echo Waiting for service to stop...
        
        REM Wait for service to stop (max 30 seconds)
        set /a counter=0
        :wait_loop
        timeout /t 2 /nobreak >nul
        set /a counter+=2
        
        sc query "%SERVICE_NAME%" | find "STOPPED" >nul
        if %errorLevel% EQU 0 (
            echo ✓ Service stopped successfully!
            goto uninstall_service
        )
        
        if %counter% LSS 30 (
            echo Still stopping... (%counter%s)
            goto wait_loop
        )
        
        echo WARNING: Service may still be running...
        echo Proceeding with uninstall anyway...
    ) else (
        echo ✗ Failed to stop service. Error code: %errorLevel%
        echo Proceeding with uninstall anyway...
    )
    echo.
)

:uninstall_service
echo Uninstalling service...
echo.

REM Delete the service
sc delete "%SERVICE_NAME%"

if %errorLevel% EQU 0 (
    echo ✓ Service uninstalled successfully!
    echo.
    
    REM Wait a moment for the system to process the deletion
    timeout /t 3 /nobreak >nul
    
    REM Verify service is gone
    sc query "%SERVICE_NAME%" >nul 2>&1
    if %errorLevel% neq 0 (
        echo ✓ Service removal verified!
        echo.
        echo ===========================================
        echo SERVICE UNINSTALLED SUCCESSFULLY!
        echo ===========================================
        echo.
        echo The LSB Log Monitor Service has been completely removed.
        echo.
        echo If you want to reinstall the service, run: StartService.bat
        echo.
    ) else (
        echo WARNING: Service may still exist in the system.
        echo You may need to restart the computer to complete removal.
        echo.
        echo Current status:
        sc query "%SERVICE_NAME%"
    )
    
) else (
    echo ✗ Failed to uninstall service. Error code: %errorLevel%
    echo.
    echo Possible causes:
    echo - Service is still running (try stopping it first)
    echo - Service is marked for deletion (restart computer)
    echo - Insufficient permissions
    echo.
    echo You can try:
    echo 1. Restarting the computer
    echo 2. Using Services.msc to delete manually
    echo 3. Running this script again after restart
    echo.
    echo Current service status:
    sc query "%SERVICE_NAME%"
)

echo.
pause
