# LSB Log Monitor Service - Console Application

## 📋 Tổng quan
LSB Log Monitor Service là ứng dụng console giám sát log files và gửi thông báo qua Telegram khi phát hiện lỗi. <PERSON><PERSON> thể chạy trực tiếp hoặc cài đặt làm Windows Service.

## 🚀 Cách sử dụng

### Chạy trực tiếp (Console Mode)
```cmd
# Chạy ứng dụng trực tiếp
LSB.LogMonitor.Service.exe

# Chạy test mode
LSB.LogMonitor.Service.exe --test
```

### Cài đặt làm Windows Service

#### 1. Cài đặt và khởi động service
```cmd
# Chạy với quyền Administrator
Scripts\StartService.bat
```
Script này sẽ:
- ✅ Kiểm tra quyền Administrator
- ✅ Kiểm tra service đã tồn tại chưa
- ✅ Cài đặt service nếu chưa có
- ✅ Khởi động service
- ✅ Cấu hình auto-start khi Windows boot

#### 2. Dừng service
```cmd
# Chạy với quyền Administrator
Scripts\StopService.bat
```

#### 3. Gỡ cài đặt service
```cmd
# Chạy với quyền Administrator
Scripts\UninstallService.bat
```

## 📁 Cấu trúc thư mục
```
LSB.LogMonitor.Service/
├── LSB.LogMonitor.Service.exe          # Ứng dụng chính
├── appsettings.json                    # Cấu hình chính
├── appsettings.Development.json        # Cấu hình development
├── Scripts/                           # Scripts quản lý service
│   ├── StartService.bat               # Cài đặt và start service
│   ├── StopService.bat                # Dừng service
│   └── UninstallService.bat           # Gỡ service
└── [Other DLL files]                  # Dependencies
```

## ⚙️ Cấu hình

### File cấu hình chính: `appsettings.json`
```json
{
    "Telegram": {
        "BotToken": "YOUR_BOT_TOKEN",
        "MainChatId": "MAIN_CHANNEL_ID",
        "Enabled": true,
        "ClientChannels": {
            "MyLinh": "CLIENT_1_CHANNEL_ID",
            "PhuongLien": "CLIENT_2_CHANNEL_ID"
        }
    },
    "LogMonitor": {
        "CheckIntervalHours": 1.0,
        "LogRootPath": "C:\\ProgramData\\DaBox\\LSBHub\\Logs"
    }
}
```

### File cấu hình client: `C:\ProgramData\LSB\logmonitor.config`
```json
{
    "accname": "Tên khách hàng hiển thị"
}
```

## 🔧 Yêu cầu hệ thống
- Windows 10/11 hoặc Windows Server 2016+
- .NET 8.0 Runtime
- Quyền Administrator (để cài đặt service)

## 📝 Logs và Monitoring

### Console Output
Khi chạy console mode, ứng dụng sẽ hiển thị:
- Thông tin khởi động
- Kết quả kiểm tra logs
- Thông báo gửi Telegram
- Lỗi (nếu có)

### Windows Event Log
Khi chạy làm service, logs được ghi vào Windows Event Viewer:
- Application and Services Logs → LSB Log Monitor Service

### Service Status
Kiểm tra trạng thái service:
```cmd
sc query LSBLogMonitorService
```

## 🚨 Troubleshooting

### Lỗi "Access Denied"
- Chạy scripts với quyền Administrator
- Kiểm tra User Account Control (UAC)

### Service không start được
- Kiểm tra Windows Event Viewer
- Đảm bảo .NET 8.0 Runtime đã cài đặt
- Kiểm tra đường dẫn executable

### Không nhận được thông báo Telegram
- Kiểm tra Bot Token và Chat ID trong appsettings.json
- Kiểm tra kết nối internet
- Chạy test mode: `LSB.LogMonitor.Service.exe --test`

### Lỗi đường dẫn logs
- Kiểm tra `LogRootPath` trong appsettings.json
- Đảm bảo thư mục logs tồn tại
- Kiểm tra quyền truy cập thư mục

## 📞 Hỗ trợ

### Kiểm tra logs chi tiết
1. Chạy test mode để kiểm tra cấu hình
2. Kiểm tra Windows Event Viewer
3. Chạy console mode để xem output trực tiếp

### Thông tin service
```cmd
# Xem thông tin chi tiết service
sc qc LSBLogMonitorService

# Xem trạng thái service
sc query LSBLogMonitorService

# Xem logs service trong Event Viewer
eventvwr.msc
```

## 🔄 Cập nhật phiên bản mới

1. Dừng service: `Scripts\StopService.bat`
2. Thay thế files mới
3. Khởi động lại: `Scripts\StartService.bat`

Hoặc:

1. Gỡ service: `Scripts\UninstallService.bat`
2. Thay thế files mới
3. Cài đặt lại: `Scripts\StartService.bat`
