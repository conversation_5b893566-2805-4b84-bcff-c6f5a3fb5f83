# LSB Telemetry Service - Console Application

## 📋 Overview
LSB Telemetry Service is a console application that monitors log files and sends notifications via Telegram when errors are detected. It can run directly or be installed as a Windows Service.

## 🚀 Usage

### Run Directly (Console Mode)
```cmd
# Run application directly
LSB.LogMonitor.Service.exe
```

### Install as Windows Service

#### 1. Install and start service
```cmd
# Run with Administrator privileges
Scripts\StartService.bat
```
This script will:
- ✅ Check Administrator privileges
- ✅ Check if service already exists
- ✅ Install service if not present
- ✅ Start the service
- ✅ Configure auto-start with Windows boot

#### 2. Stop service
```cmd
# Run with Administrator privileges
Scripts\StopService.bat
```

#### 3. Uninstall service
```cmd
# Run with Administrator privileges
Scripts\UninstallService.bat
```

## 📁 Directory Structure
```
LSB.LogMonitor.Service/
├── LSB.LogMonitor.Service.exe          # Main application
├── appsettings.json                    # Main configuration
├── appsettings.Development.json        # Development configuration
├── Scripts/                           # Service management scripts
│   ├── StartService.bat               # Install and start service
│   ├── StopService.bat                # Stop service
│   └── UninstallService.bat           # Uninstall service
└── [Other DLL files]                  # Dependencies
```

## ⚙️ Configuration

### Main configuration file: `appsettings.json`
```json
{
    "Telegram": {
        "BotToken": "YOUR_BOT_TOKEN",
        "MainChatId": "MAIN_CHANNEL_ID",
        "Enabled": true,
        "ClientChannels": {
            "Client1": "CLIENT_1_CHANNEL_ID",
            "Client2": "CLIENT_2_CHANNEL_ID"
        }
    },
    "LogMonitor": {
        "CheckIntervalHours": 1.0,
        "LogRootPath": "C:\\ProgramData\\DaBox\\LSBHub\\Logs"
    }
}
```

### Client configuration file: `C:\ProgramData\LSB\logmonitor.config`
```json
{
    "accname": "Client Display Name"
}
```

## 🔧 System Requirements
- Windows 10/11 or Windows Server 2016+
- .NET 8.0 Runtime
- Administrator privileges (for service installation)

## 📝 Logs and Monitoring

### Console Output
When running in console mode, the application displays:
- Startup information
- Log check results
- Telegram notification status
- Errors (if any)

### Windows Event Log
When running as a service, logs are written to Windows Event Viewer:
- Application and Services Logs → LSB Telemetry Service

### Service Status
Check service status:
```cmd
sc query LSBTelemetryService
```

## 🚨 Troubleshooting

### "Access Denied" Error
- Run scripts with Administrator privileges
- Check User Account Control (UAC)

### Service won't start
- Check Windows Event Viewer
- Ensure .NET 8.0 Runtime is installed
- Verify executable path

### No Telegram notifications
- Check Bot Token and Chat ID in appsettings.json
- Check internet connection
- Run application in console mode to see error messages

### Log path errors
- Check `LogRootPath` in appsettings.json
- Ensure logs directory exists
- Check directory access permissions

## 📞 Support

### Check detailed logs
1. Run console mode to see direct output
2. Check Windows Event Viewer
3. Check service logs in Event Viewer

### Service information
```cmd
# View detailed service information
sc qc LSBTelemetryService

# View service status
sc query LSBTelemetryService

# View service logs in Event Viewer
eventvwr.msc
```

## 🔄 Update to New Version

1. Stop service: `Scripts\StopService.bat`
2. Replace files with new version
3. Restart: `Scripts\StartService.bat`

Or:

1. Uninstall service: `Scripts\UninstallService.bat`
2. Replace files with new version
3. Reinstall: `Scripts\StartService.bat`
