# Essential executable files
LSB.LogMonitor.Service/bin/Release/net8.0/LSB.LogMonitor.Service.exe
LSB.LogMonitor.Service/bin/Release/net8.0/LSB.LogMonitor.Service.dll
LSB.LogMonitor.Service/bin/Release/net8.0/LSB.LogMonitor.Service.runtimeconfig.json
LSB.LogMonitor.Service/bin/Release/net8.0/LSB.LogMonitor.Service.deps.json

# Microsoft Extensions dependencies
LSB.LogMonitor.Service/bin/Release/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll
LSB.LogMonitor.Service/bin/Release/net8.0/Microsoft.Extensions.Configuration.Binder.dll
LSB.LogMonitor.Service/bin/Release/net8.0/Microsoft.Extensions.Configuration.CommandLine.dll
LSB.LogMonitor.Service/bin/Release/net8.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll
LSB.LogMonitor.Service/bin/Release/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll
LSB.LogMonitor.Service/bin/Release/net8.0/Microsoft.Extensions.Configuration.Json.dll
LSB.LogMonitor.Service/bin/Release/net8.0/Microsoft.Extensions.Configuration.UserSecrets.dll
LSB.LogMonitor.Service/bin/Release/net8.0/Microsoft.Extensions.Configuration.dll
LSB.LogMonitor.Service/bin/Release/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll
LSB.LogMonitor.Service/bin/Release/net8.0/Microsoft.Extensions.DependencyInjection.dll
LSB.LogMonitor.Service/bin/Release/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.dll
LSB.LogMonitor.Service/bin/Release/net8.0/Microsoft.Extensions.Diagnostics.dll
LSB.LogMonitor.Service/bin/Release/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll
LSB.LogMonitor.Service/bin/Release/net8.0/Microsoft.Extensions.FileProviders.Physical.dll
LSB.LogMonitor.Service/bin/Release/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll
LSB.LogMonitor.Service/bin/Release/net8.0/Microsoft.Extensions.Hosting.Abstractions.dll
LSB.LogMonitor.Service/bin/Release/net8.0/Microsoft.Extensions.Hosting.WindowsServices.dll
LSB.LogMonitor.Service/bin/Release/net8.0/Microsoft.Extensions.Hosting.dll
LSB.LogMonitor.Service/bin/Release/net8.0/Microsoft.Extensions.Http.dll
LSB.LogMonitor.Service/bin/Release/net8.0/Microsoft.Extensions.Logging.Abstractions.dll
LSB.LogMonitor.Service/bin/Release/net8.0/Microsoft.Extensions.Logging.Configuration.dll
LSB.LogMonitor.Service/bin/Release/net8.0/Microsoft.Extensions.Logging.Console.dll
LSB.LogMonitor.Service/bin/Release/net8.0/Microsoft.Extensions.Logging.Debug.dll
LSB.LogMonitor.Service/bin/Release/net8.0/Microsoft.Extensions.Logging.EventLog.dll
LSB.LogMonitor.Service/bin/Release/net8.0/Microsoft.Extensions.Logging.EventSource.dll
LSB.LogMonitor.Service/bin/Release/net8.0/Microsoft.Extensions.Logging.dll
LSB.LogMonitor.Service/bin/Release/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll
LSB.LogMonitor.Service/bin/Release/net8.0/Microsoft.Extensions.Options.dll
LSB.LogMonitor.Service/bin/Release/net8.0/Microsoft.Extensions.Primitives.dll

# System dependencies
LSB.LogMonitor.Service/bin/Release/net8.0/System.CodeDom.dll
LSB.LogMonitor.Service/bin/Release/net8.0/System.Configuration.ConfigurationManager.dll
LSB.LogMonitor.Service/bin/Release/net8.0/System.Diagnostics.EventLog.dll
LSB.LogMonitor.Service/bin/Release/net8.0/System.Diagnostics.PerformanceCounter.dll
LSB.LogMonitor.Service/bin/Release/net8.0/System.IO.Pipelines.dll
LSB.LogMonitor.Service/bin/Release/net8.0/System.Management.dll
LSB.LogMonitor.Service/bin/Release/net8.0/System.Security.Cryptography.ProtectedData.dll
LSB.LogMonitor.Service/bin/Release/net8.0/System.ServiceProcess.ServiceController.dll
LSB.LogMonitor.Service/bin/Release/net8.0/System.Text.Encodings.Web.dll
LSB.LogMonitor.Service/bin/Release/net8.0/System.Text.Json.dll

# Third-party dependencies
LSB.LogMonitor.Service/bin/Release/net8.0/YamlDotNet.dll

# Utility scripts
LSB.LogMonitor.Service/Scripts/StartService.bat
LSB.LogMonitor.Service/Scripts/StopService.bat
LSB.LogMonitor.Service/Scripts/UninstallService.bat
LSB.LogMonitor.Service/Scripts/SetupConfig.bat

# Documentation and templates
appsettings.template.json
README.txt
